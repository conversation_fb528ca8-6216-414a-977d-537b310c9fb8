{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1a48ca26", "metadata": {}, "outputs": [], "source": ["import struct\n", "\n", "# 将 hex 字符串转换为 bytes\n", "hex_string = '00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'\n", "hex_bytes = bytes.fromhex(hex_string)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a1096796", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length bytes: 0032 → Parsed length: (50,)\n"]}], "source": ["# 提取前两个字节作为长度字段\n", "length_data = hex_bytes[0:2]\n", "length = struct.unpack('>H', length_data)[0]\n", "\n", "print(f\"Length bytes: {length_data.hex()} → Parsed length: {length}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "310338ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["func_code = struct.unpack('>H', hex_bytes[2:4])[0]  # 大端\n", "func_code== 0x8301"]}, {"cell_type": "code", "execution_count": 16, "id": "a06c877b", "metadata": {}, "outputs": [{"data": {"text/plain": ["10000"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": [" device_version =struct.unpack('>H', hex_bytes[28:30])[0]\n", " device_version"]}, {"cell_type": "code", "execution_count": 9, "id": "8a56328c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'3402024203007157'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["device_number = hex_bytes[30:46].decode('ascii', errors='ignore').strip('\\x00')\n", "device_number"]}, {"cell_type": "code", "execution_count": 12, "id": "6979194a", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\xb0\\x13\\x00\\x00'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": [" hex_bytes[46:50]"]}, {"cell_type": "code", "execution_count": 10, "id": "34c30a33", "metadata": {}, "outputs": [{"data": {"text/plain": ["2954035200"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["checksum = struct.unpack('>I', hex_bytes[46:50])[0]\n", "checksum"]}, {"cell_type": "code", "execution_count": 11, "id": "3ecde2f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["5040"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cal_checkesun =sum(hex_bytes[0:46]) & 0xFFFFFFFF\n", "cal_checkesun"]}, {"cell_type": "code", "execution_count": 14, "id": "af481a17", "metadata": {}, "outputs": [{"data": {"text/plain": ["'NTB2GV31'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["program_id = hex_bytes[4:12].decode('ascii', errors='ignore').strip('\\x00')\n", "program_id\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}