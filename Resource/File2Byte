/**
 *
 *文件下载转换为二进制流
 * @param filePath
 * @return
 */
public byte[] downloadToFile(String filePath){
    //实例化OSSClient对象
    byte[] base64Str1=null;
    //截取名称 //存放在file文件下的
    String objectName = filePath.substring(filePath.indexOf("ota/"));
    GetObjectRequest request = new GetObjectRequest(awss3Config.getBucketName(), objectName);
    S3Object response = amazonS3.getObject(request);
    ByteArrayOutputStream outStream = new ByteArrayOutputStream();
    try {
        InputStream inStream = response.getObjectContent();
        byte[] buffer = new byte[1024];
        int len = -1;
        while((len = inStream.read(buffer)) != -1){
            outStream.write(buffer, 0, len);
        }
        outStream.close();
        inStream.close();
        base64Str1= outStream.toByteArray();//变成了二进制数组
    } catch (IOException e) {
        log.info("download error {} ", e.getMessage());
    }
    return base64Str1;
}