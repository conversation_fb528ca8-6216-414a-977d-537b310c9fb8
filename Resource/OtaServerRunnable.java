package com.gotion.ota.tcp;

import com.gotion.ota.config.AWSS3Config;
import com.gotion.ota.config.AliOssConfig;
import com.gotion.ota.constant.OtaSID;
import com.gotion.ota.entity.OtaUpgrade;
import com.gotion.ota.entity.OtaUpgradeLogDetails;
import com.gotion.ota.entity.PileDeviceVersion;
import com.gotion.ota.mapper.OtaUpgradeLogDetailsMapper;
import com.gotion.ota.mapper.OtaUpgradeMapper;
import com.gotion.ota.mapper.PileDeviceMapper;
import com.gotion.ota.mapper.PileDeviceVersionMapper;
import com.gotion.ota.util.AWSS3Util;
import com.gotion.ota.util.AliOssUtil;
import com.gotion.ota.util.SpringContextUtils;
import com.gotion.ota.vo.OtaDataVo;
import com.gotion.ota.vo.ResultPileDeviceVo;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.Socket;
import java.util.Date;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Logger;

import static com.gotion.ota.constant.OtaCode.*;
import static com.gotion.ota.util.ByteUtil.*;
import static com.gotion.ota.util.ByteUtil.bytesToHexString;

/**
 * @ClassName: ServerReaderRunnable
 * @Description:
 * @Author: cy
 * @version: 1.0
 * @Date: 2023/7/17 15:59
 */
public class OtaServerRunnable implements Runnable {
    private Logger logger = Logger.getGlobal();

    protected volatile ConcurrentLinkedQueue<byte[]> dataQueue = new ConcurrentLinkedQueue<>();

    private Socket socket;

    private PileDeviceVersionMapper pileDeviceVersionMapper;

    private OtaUpgradeMapper otaUpgradeMapper;

    private OtaUpgradeLogDetailsMapper otaUpgradeLogDetailsMapper;

    private PileDeviceMapper pileDeviceMapper;

    private AliOssUtil aliOssUtil;
    private AliOssConfig aliOssConfig;

    private AWSS3Util awss3Util;
    private AWSS3Config awss3Config;

    public OtaServerRunnable(Socket socket) {
        this.socket = socket;
        this.pileDeviceVersionMapper = SpringContextUtils.getBeanByClass(PileDeviceVersionMapper.class);
        this.otaUpgradeMapper = SpringContextUtils.getBeanByClass(OtaUpgradeMapper.class);
        this.otaUpgradeLogDetailsMapper = SpringContextUtils.getBeanByClass(OtaUpgradeLogDetailsMapper.class);
        this.pileDeviceMapper = SpringContextUtils.getBeanByClass(PileDeviceMapper.class);
        this.aliOssUtil = SpringContextUtils.getBeanByClass(AliOssUtil.class);
        this.aliOssConfig = SpringContextUtils.getBeanByClass(AliOssConfig.class);
        this.awss3Util = SpringContextUtils.getBeanByClass(AWSS3Util.class);
        this.awss3Config = SpringContextUtils.getBeanByClass(AWSS3Config.class);
    }

    @Override
    public void run() {
        InputStream inputStream = null;
        String vin = null;
        boolean flag = true;
        byte[] fileBytes = null;
        try {
            inputStream = socket.getInputStream();
            OtaDataVo otaDataVo = null;
            OtaUpgrade otaUpgrade =null;
            while(flag){
                try {
                    socket.sendUrgentData(0);
                } catch (IOException e) {
                    flag = false;
                    break;
                }
                byte[] data = new byte[64];
                inputStream.read(data);
                //logger.info("收到"+socket.getRemoteSocketAddress()+"的消息:"+bytesToHexString(data));
                String code = bytesToHexString(new byte[]{data[2],data[3]}).replaceAll(" ", "");
                if (code.equals(GET_VERSION_DEVICE.getType())) {
                    logger.info("收到"+socket.getRemoteSocketAddress() + "收到设备向服务器发送获取版本信息:"+byteToString(data));
                    String pileNo = bytesToAscii(data,33,13);
                    ResultPileDeviceVo resultPileDeviceVo = pileDeviceMapper.getVinByCode(pileNo);
                    if (resultPileDeviceVo==null) {
                        continue;
                    }
                    vin = resultPileDeviceVo.getVin();
                    otaDataVo = new OtaDataVo();
                    otaDataVo.setPileNo(pileNo);
                    otaDataVo.setDeviceId(resultPileDeviceVo.getId());
                    otaDataVo.setVin(resultPileDeviceVo.getVin());
                    otaDataVo.setVer(bytesToHexString(subBytes(data,12,4)));
                    otaDataVo.setProcedureId(bytesToHexString(subBytes(data,4,8)));
                    otaDataVo.setSid(bytesToAscii(data,4,8));
                    otaUpgrade = otaUpgradeMapper.getFailByVin(otaDataVo.getVin());
                    if (otaUpgrade!=null) {
                        //下载升级包
                        fileBytes = awss3Util.downloadToFile(otaUpgrade.getAfterChangeUrl());     //根据地址，从aws下载升级包文件
                    }
                    getVersionDevice(subBytes(data,0,50),otaDataVo,otaUpgrade);
                }
                if (code.equals(GET_PROCEDURE_DEVICE.getType())&&otaDataVo!=null) {
                    logger.info("收到"+socket.getRemoteSocketAddress() + "收到设备向服务器发送获取程序信息:"+byteToString(data));
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())&&otaUpgrade.getType()==1) {
                        getProcedureDevice(subBytes(data,0,8),fileBytes);
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())&&otaUpgrade.getType()==0) {
                        getProcedureDevice(subBytes(data,0,8),fileBytes);
                    } else {
                        continue;
                    }
                }
                if (code.equals(DOWNLOAD_PROCEDURE_DEVICE.getType())&&otaDataVo!=null) {
                    if (fileBytes==null) {
                        fileBytes = awss3Util.downloadToFile(otaUpgrade.getAfterChangeUrl());
                        //判断平台是否有未开始的状态，有则修改为升级中
                        OtaUpgradeLogDetails otaUpgradeLogDetails = otaUpgradeLogDetailsMapper.getNewByVin(otaDataVo.getVin());
                        if (otaUpgradeLogDetails!=null && otaUpgradeLogDetails.getState()==0) {
                            otaUpgradeLogDetails.setState(2);
                            otaUpgradeLogDetailsMapper.update(otaUpgradeLogDetails);
                        }
                    }
                    logger.info("收到"+socket.getRemoteSocketAddress() + "收到设备向服务器发送获取升级包信息:"+byteToString(data));
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())&&otaUpgrade.getType()==1) {

                        downloadProcedureDevice(subBytes(data,0,14),otaDataVo,fileBytes);
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())&&otaUpgrade.getType()==0) {
                        downloadProcedureDevice(subBytes(data,0,14),otaDataVo,fileBytes);
                    } else {
                        continue;
                    }
                }
                if (code.equals(SEND_START.getType())&&otaDataVo!=null) {
                    logger.info("收到"+socket.getRemoteSocketAddress() + "收到设备向服务器发送启动信息:"+byteToString(data));
                    sendStart(subBytes(data,0,36),otaDataVo);
                }
            }
        } catch (Exception e) {
            logger.info(socket.getRemoteSocketAddress() + "下线了");
        } finally {
            //断开连接则改为未升级/升级失败
            if (!StringUtils.isEmpty(vin)) {
                //判断平台是否有进行中的状态，有则修改为失败
                logger.info("重置" + vin + "升级状态");
                OtaUpgradeLogDetails otaUpgradeLogDetails = otaUpgradeLogDetailsMapper.getNewByVin(vin);
                if (otaUpgradeLogDetails!=null && otaUpgradeLogDetails.getState()==2) {
                    otaUpgradeLogDetails.setState(0);
                    otaUpgradeLogDetailsMapper.update(otaUpgradeLogDetails);
                }
            }
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (socket != null) {
                    socket.close();
                }
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

    /** 接收：00 32 83 01 4e 54 42 32 47 56 33 31 fe 07 00 00 ff ff ff ff ff ff ff ff ff ff ff ff 33 34 30 31 32 30 32 33 30 32 32 36 30 30 30 31 64 00 44 13 00 00
     * 00 32
     * 83 01
     * 4e 54 42 32 47 56 33 31
     * fe
     * 07
     * 00
     * 00
     * ff ff ff ff ff ff ff ff ff ff ff ff
     * 33 34
     * 30 31 32 30   32 33 30 32   32 36 30 30   30 31 64 00  01202302260001d0   00 00 00 32   30 32 33 32   33 33 32 30   36 30 30 31： 2023 23320 6001
     *
     * 44 13 00 00
     *
     * 回复：00 18 83 02 75 27 47 54 42 32 47 56 33 31 a1 64 64 64 64 00 7a 05 00 00
     * 00 18
     * 83 02
     * 75 27
     * 47 54 42 32 47 56 33 31
     * a1
     * 64
     * 64
     * 64
     * 64
     * 00
     * 7a 05 00 00
     * 测试用：00 32 83 01 4e 54 42 32 47 56 33 31 fe 07 00 00 ff ff ff ff ff ff ff ff ff ff ff ff 33 34 32 30 32 33 32 33 33 32 30 36 30 30 31 00 00 00 44 13 00 00
     * */
    void getVersionDevice(byte[] data,OtaDataVo otaDataVo,OtaUpgrade otaUpgrade) {
        try {
            //校验和
            byte[] sum = subBytes(data,46,4);
            byte[] sumCheck = makeChecksum(subBytes(data,0,46),4);
            boolean isTure = equalsArr(littleToBigEndian(sumCheck),sum);
            if (isTure) {
                //芯片id,暂时用不到，设备发ff
                //byte[] chipId = littleToBigEndian(subBytes(data,16,12));
                int deviceProcedureVersion = byteToInt(data[29],data[28]);
                PileDeviceVersion pileDeviceVersion = pileDeviceVersionMapper.getByCode(otaDataVo.getPileNo());
                byte[] pv = new byte[2];
                String upgrade = "A0";
                if (otaUpgrade!=null) {
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())&&otaUpgrade.getType()==1) {
                        upgrade = "A1";
                        pv = int2bytes(Integer.valueOf(otaUpgrade.getPlatformVersion()));
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())&&otaUpgrade.getType()==0) {
                        upgrade = "A1";
                        pv = int2bytes(Integer.valueOf(otaUpgrade.getPlatformVersion()));
                    } else {
                        if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())) {
                            pv = int2bytes(Integer.valueOf(pileDeviceVersion.getBmsVersion()));
                        } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())) {
                            pv = int2bytes(Integer.valueOf(pileDeviceVersion.getDeviceProcedureVersion()));
                        }
                    }
                } else {
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())) {
                        pv = int2bytes(Integer.valueOf(pileDeviceVersion.getBmsVersion()));
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())) {
                        pv = int2bytes(Integer.valueOf(pileDeviceVersion.getDeviceProcedureVersion()));
                    }
                }
                if (pileDeviceVersion==null) {
                    pileDeviceVersion = new PileDeviceVersion();
                    pileDeviceVersion.setDeviceId(otaDataVo.getDeviceId());
                    pileDeviceVersion.setProcedureId(otaDataVo.getProcedureId());
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())) {
                        pileDeviceVersion.setBmsVersion(String.valueOf(deviceProcedureVersion));
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())) {
                        pileDeviceVersion.setDeviceProcedureVersion(String.valueOf(deviceProcedureVersion));
                    }
                    pileDeviceVersion.setCreateTime(new Date());
                    pileDeviceVersion.setUpdateTime(new Date());
                    pileDeviceVersionMapper.insert(pileDeviceVersion);
                } else {
                    if (otaDataVo.getSid().equals(OtaSID.BMS_SID.getType())) {
                        pileDeviceVersion.setBmsVersion(String.valueOf(deviceProcedureVersion));
                    } else if (otaDataVo.getSid().equals(OtaSID.CCU_SID.getType())) {
                        pileDeviceVersion.setDeviceProcedureVersion(String.valueOf(deviceProcedureVersion));
                    }
                    pileDeviceVersion.setUpdateTime(new Date());
                    pileDeviceVersionMapper.update(pileDeviceVersion);
                }

                String platformProcedureVersion = byteToHex(pv[0]) + " " + byteToHex(pv[1]);

                StringBuffer returnData = new StringBuffer();
                returnData.append("00 18 83 02 ");
                returnData.append(platformProcedureVersion + " ");
                returnData.append(otaDataVo.getProcedureId() + " ");
                returnData.append(upgrade + " ");
                returnData.append(otaDataVo.getVer() + " ");
                returnData.append("00");
                byte[] d = makeChecksum(hexStringToByteArray(returnData.toString()),4);
                returnData.append(" " + bytesToHexString(littleToBigEndian(d)));
                logger.info("回复"+socket.getRemoteSocketAddress() + ":"+returnData.toString());
                byte[] returnHex = hexStringToByteArray(returnData.toString());
                //使用输出流给发送者发送一条成功接收的信息
                OutputStream outputStream = null;
                outputStream = socket.getOutputStream();
                outputStream.write(returnHex);
                // 只要涉及管道的都建议刷新一下
                outputStream.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 接收：00 08 84 03 8f 00 00 00
     * 00 08
     * 84 03
     * 8f 00 00 00
     *
     * 回复：00 48 84 04 75 27 17 07 0a 11 06 1e b5 89 94 4a 4d c6 04 c3 8f bd 02 00 98 35 2e 01 47 54 42 32 47 56 33 31 64 64 64 64 45 75 72 6f 70 65 61 6e 20 73 74 61 6e 64 61 72 64 00 00 00 00 00 00 00 00 00 00 00 59 12 00 00
     * 00 48
     * 84 04
     *   75 27
     *   17 07 0a 11 06 1e     ----  23 07 10 17 06 30
     *   b5 89 94 4a 4d c6 04 c3
     *   8f bd 02 00  2189143
     *   98 35 2e 01
     *   47 54 42 32 47 56 33 31
     *   64
     *   64
     *   64 64
     *   45 75 72 6f 70 65 61 6e 20 73 74 61 6e 64 61 72 64 00 00 00 00 00 00 00
     *   00 00 00 00
     * 59 12 00 00
     *
     * */
    void getProcedureDevice(byte[] data,byte[] fileBytes) {
        try {
            //校验和
            byte[] sum = subBytes(data,4,4);
            byte[] sumCheck = makeChecksum(subBytes(data,0,4),4);
            boolean isTure = equalsArr(littleToBigEndian(sumCheck),sum);
            if (isTure) {
                //String filepath = "E:\\Workspace\\gotion\\ocpp-ota\\file\\NT-B2G-V3_Ex.yxp";
                //String filepath = "/home/<USER>/ocpp-ota/file/NT-B2G-V3_Ex_10000_100_100_100_100_20230823054620.yxpbk";
                //byte[] bytes = Files.readAllBytes(Paths.get(filepath));

                StringBuffer returnData = new StringBuffer();
                returnData.append("00 48 84 04 ");
                returnData.append(bytesToHexString(subBytes(fileBytes,0,64)));

                byte[] d = makeChecksum(hexStringToByteArray(returnData.toString()),4);
                returnData.append(" " + bytesToHexString(littleToBigEndian(d)));
                logger.info("回复："+returnData.toString());
                byte[] returnHex = hexStringToByteArray(returnData.toString());
                //使用输出流给发送者发送一条成功接收的信息
                OutputStream outputStream = null;
                outputStream = socket.getOutputStream();
                outputStream.write(returnHex);
                // 只要涉及管道的都建议刷新一下
                outputStream.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 接收：00 0e 91 01 00 f4 01 00 04 00 99 01 00 00
     * 00 10
     * 91 01
     * 00 00 00 01
     * 8f 00
     * 32 01 00 00
     ** */
    void downloadProcedureDevice(byte[] data,OtaDataVo otaDataVo,byte[] fileBytes) {
        try {
            //校验和
            byte[] sum = subBytes(data,10,4);
            byte[] sumCheck = makeChecksum(subBytes(data,0,10),4);
            boolean isTure = equalsArr(littleToBigEndian(sumCheck),sum);
            if (isTure) {
                long index = Long.parseLong(byteToHex(data[7])+byteToHex(data[6])+byteToHex(data[5])+byteToHex(data[4]),16);
                long len = byteToInt(data[9],data[8]);
                if (index==0L) {
                    //判断平台是否有未开始的状态，有则修改为升级中
                    OtaUpgradeLogDetails otaUpgradeLogDetails = otaUpgradeLogDetailsMapper.getNewByVin(otaDataVo.getVin());
                    if (otaUpgradeLogDetails!=null && otaUpgradeLogDetails.getState()==0) {
                        otaUpgradeLogDetails.setState(2);
                        otaUpgradeLogDetailsMapper.update(otaUpgradeLogDetails);
                    }
                }

                byte[] bytes = subBytes(fileBytes,64,fileBytes.length-64);
                //byte[] proc = subBytes(bytes,(int) (index*len),bytes.length/len<=index?bytes.length%(int)len:(int)len);
                byte[] proc = subBytes(bytes,(int) index, index+len<bytes.length?(int)len: (int) (bytes.length - index));
                byte[] pv = int2bytes(proc.length);
                byte[] re_len = int2bytes(proc.length+14);
                String lenStr = byteToHex(re_len[0]) + " " + byteToHex(re_len[1]);
                StringBuffer returnData = new StringBuffer();
                returnData.append(lenStr + " ");
                returnData.append("91 02 ");
                returnData.append(bytesToHexString(subBytes(data,4,4)) + " ");
                returnData.append(byteToHex(pv[0]) + " " + byteToHex(pv[1]) + " ");
                returnData.append(bytesToHexString(proc)+ " ");

                byte[] d = makeChecksum(hexStringToByteArray(returnData.toString()),4);
                returnData.append(bytesToHexString(littleToBigEndian(d)));
                logger.info(returnData.toString());
                byte[] returnHex = hexStringToByteArray(returnData.toString());
                //使用输出流给发送者发送一条成功接收的信息
                OutputStream outputStream = null;
                outputStream = socket.getOutputStream();
                outputStream.write(returnHex);
                // 只要涉及管道的都建议刷新一下
                outputStream.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送：00 20 83 03 4e 54 42 32 47 56 33 31 33 34 00 00 00 32 30 32 33 32 33 33 32 30 36 30 30 31 33 34
     * 00 20
     * 83 03
     * 4e 54 42 32 47 56 33 31
     * 33 34
     * 00 00 00 32 30 32 33 32 33 33 32 30 36 30 30 31
     * 33 34
     * */
    void sendStart(byte[] data,OtaDataVo otaDataVo) {
        try {
            //判断平台是否有进行中的状态，有则修改为成功
            OtaUpgradeLogDetails otaUpgradeLogDetails = otaUpgradeLogDetailsMapper.getNewByVin(otaDataVo.getVin());
            if (otaUpgradeLogDetails!=null) {
                otaUpgradeLogDetails.setState(1);
                otaUpgradeLogDetails.setUpdateTime(new Date());
                otaUpgradeLogDetailsMapper.update(otaUpgradeLogDetails);
            }

            logger.info("回复："+byteToString(data));
            //使用输出流给发送者发送一条成功接收的信息
            OutputStream outputStream = null;
            outputStream = socket.getOutputStream();
            outputStream.write(data);
            // 只要涉及管道的都建议刷新一下
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        //2023233206001
        //NTB2GV31
        //RNBMSV20
        String ms = "00 32 83 01 48 59 43 48 47 56 31 30 01 02 03 04 4e 58 50 53 33 32 4b 4c 50 43 31 36 00 65 30 30 30 32 30 32 33 32 33 33 32 30 30 30 30 31 a0 09 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00";
        String pileNo = bytesToAscii(hexStringToByteArray(ms),33,13);
        //String sid = bytesToAscii(hexStringToByteArray(ms),4,8);
        //byte[] data=subBytes(hexStringToByteArray(ms),0,50);
        //int deviceProcedureVersion = byteToInt(data[29],data[28]);
        System.out.println(pileNo);
        //System.out.println(deviceProcedureVersion);
        //System.out.println(sid);

        String ou = "00 18 83 02 27 1a 52 4e 42 4d 53 56 32 30 A1 64 64 64 64 00 49 05 00 00";
        System.out.println();

        String ss = "00 32 83 01 4e 54 42 32 47 56 33 31 01 02 03 04 4e 58 50 53 33 32 4b 4c 50 43 31 36 01 00 30 30 30 32 30 32 33 32 33 33 32 30 30 30 30 31 29 09 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00";
        byte[] data=subBytes(hexStringToByteArray(ss),0,50);
        long index = Long.parseLong(byteToHex(data[7])+byteToHex(data[6])+byteToHex(data[5])+byteToHex(data[4]),16);
        long len = byteToInt(data[9],data[8]);
        System.out.println(index);
        System.out.println(len);
    }
}