#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN协议常量定义
"""

from enum import Enum, IntEnum


class UpgradeControl(Enum):
    """升级控制枚举"""
    FORBIDDEN = 0xA0      # 服务器禁止此设备升级
    VERSION_CHECK = 0xA1  # 设备发现版本更新了就升级
    FORCE_UPGRADE = 0xA2  # 立即升级，判断版本一致才下载
    FORCE_REPLACE = 0xA3  # 立即升级，不判断版本
    NO_SUITABLE = 0xA4    # 服务器没有适合该版本程序


class FunctionCode(Enum):
    """功能代码枚举"""
    # 获取版本信息
    GET_VERSION_REQ = 0x8301
    GET_VERSION_RESP = 0x8302
    
    # 获取程序信息
    GET_PROGRAM_INFO_REQ = 0x8403
    GET_PROGRAM_INFO_RESP = 0x8404
    
    # 下载程序数据
    DOWNLOAD_DATA_REQ = 0x9101
    DOWNLOAD_DATA_RESP = 0x9102
    
    # 发送启动信息
    SEND_STARTUP_REQ = 0x8303
    SEND_STARTUP_RESP = 0x8303  # 原封不动返回




class OTASID(IntEnum):
    """OTA 升级类型：0 - CCU, 1 - BMS"""
    CCU = 0
    BMS = 1

    @property
    def byte_value(self) -> bytes:
        """获取字节表示"""
        return self._byte_map[self]

    @classmethod
    def from_byte_value(cls, byte_val: bytes):
        """根据字节值返回枚举成员"""
        for k, v in cls._byte_map.items():
            if v == byte_val:
                return cls(k)
        return None

    @classmethod
    def get_value_by_byte(cls, byte_val: bytes) -> int:
        """字节值转 program_id (int)"""
        member = cls.from_byte_value(byte_val)
        return member.value if member else None

    @classmethod
    def get_byte_by_value(cls, value: int) -> bytes:
        """program_id (int) 转字节值"""
        try:
            return cls(value).byte_value
        except ValueError:
            return None
