#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gRPC客户端，用于查询平台端升级包信息
"""

import logging
from typing import Optional, Dict
from .constants import OTASID

logger = logging.getLogger(__name__)


class GRPCClient:
    """gRPC客户端"""
    
    def __init__(self, grpc_server_url: str):
        self.grpc_server_url = grpc_server_url
        # TODO: 初始化 gRPC 连接
    
    def query_upgrade_info(self, device_id: str, program_id: str) -> Optional[Dict]:
        """
        查询升级包信息
        
        Args:
            device_id: 设备编码
            program_id: 升级程序类型 (0:CCU,1:BMS)
            
        Returns:
            升级包信息字典，包含：
            - device_id: 设备编码
            - file_url: 文件地址
            - upgrade_type: (0:CCU,1:BMS)
            - platform_version: 平台版本
        """
        try:
            upgrade_type = OTASID.get_value_by_byte(program_id)
            logger.info(f"请求设备: {device_id}, 类型为: {upgrade_type} 的升级信息")
            
            # TODO: 实现实际的 gRPC 调用
            # gRPC request content: device_id, update_type
            
            # 模拟返回数据
            mock_response = {
                'device_id': device_id,
                'file_url': 'ota/4c4e96cf-9937-4299-b113-5948bb7f2e53_ccu-10045_NT-B2G-V3 2024.7.10_Ex.yxp',
                'program_id': OTASID.get_byte_by_value(upgrade_type),
                'platform_version': '10400',
            }
            
            logger.info(f"Received upgrade info: {mock_response}")
            return mock_response
            
        except Exception as e:
            logger.error(f"Failed to query upgrade info: {e}")
            return None
        
    def download_file_content(self, file_url: str) -> Optional[bytes]:
        """
        通过 gRPC 下载文件内容
        
        Args:
            file_url: 文件地址
            
        Returns:
            文件的二进制数据，失败时返回 None
        """
        try:
            logger.info(f"Downloading file content via gRPC: {file_url}")
            
            # TODO: 实现实际的 gRPC 调用获取文件内容
            # 平台会以 byte 数组形式返回文件
            
            # 模拟返回文件数据
            mock_file_data = b"mock_file_content_" * 1000  # 模拟文件内容
            
            logger.info(f"Successfully downloaded {len(mock_file_data)} bytes via gRPC")
            return mock_file_data
            
        except Exception as e:
            logger.error(f"Failed to download file content: {e}")
            return None
    
    def query_file_size(self, file_url: str) -> Optional[int]:
        """
        查询文件大小
        
        Args:
            file_url: 文件地址
            
        Returns:
            文件大小（字节），失败时返回 None
        """
        try:
            # TODO: 实现实际的 gRPC 调用
            return 1024  # 模拟返回 1KB
        except Exception as e:
            logger.error(f"Failed to query file size: {e}")
            return None
