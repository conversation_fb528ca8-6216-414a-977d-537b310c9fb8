#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN协议服务器
"""

import socket
import threading
import logging
from typing import Optional
from .program_manager import ProgramManager
from .client_handler import ClientHandler
from .grpc_client import GRPCClient

logger = logging.getLogger(__name__)


class UFNServer:
    """UFN协议服务器"""
    
    def __init__(self, host='0.0.0.0', port=8080, timeout=10, grpc_server_url: str = None):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.running = False
        
        # 初始化 gRPC 客户端
        self.grpc_client = GRPCClient(grpc_server_url) if grpc_server_url else None
        
        # 初始化组件
        self.program_manager = ProgramManager(self.grpc_client)
        self.client_handler = ClientHandler(self.program_manager, self.grpc_client, timeout)
    
    def start(self):
        """启动服务器"""
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(5)
            self.running = True
            
            logger.info(f"UFN Server started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, client_address = self.socket.accept()
                    client_thread = threading.Thread(
                        target=self.client_handler.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        logger.error(f"Accept error: {e}")
        
        except Exception as e:
            logger.error(f"Server start error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.socket:
            self.socket.close()
        logger.info("UFN Server stopped")
