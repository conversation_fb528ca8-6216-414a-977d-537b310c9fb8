#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN协议消息构建器
"""

import struct
from typing import Dict
from .constants import FunctionCode, UpgradeControl,OTASID
from .message_parser import MessageParser


class MessageBuilder:
    """UFN协议消息构建器"""
    
    @staticmethod
    def create_get_version_response(reponse_data: Dict) -> bytes:
        """创建获取版本信息响应(8302)"""
        if reponse_data['upgrade_available']:
            upgrade_control = UpgradeControl.FORBIDDEN.value
        else:
            upgrade_control = UpgradeControl.VERSION_CHECK.value

        # 构造响应报文
        response = bytearray()
        response.extend(struct.pack('>H', 0x0018))  # 报文长度
        response.extend(struct.pack('>H', FunctionCode.GET_VERSION_RESP.value))  # 功能代码
        response.extend(struct.pack('>H', reponse_data['platform_version']))  # 服务器程序版本号
        #response.extend(bytes.fromhex(program_id))  # 程序ID
        response.extend(struct.pack('B', reponse_data['upgrade_type']))
        response.extend(struct.pack('B', upgrade_control))  # 升级控制
        response.extend(struct.pack('B', reponse_data['area']))  # Area
        response.extend(struct.pack('B', reponse_data['option']))  # Option
        response.extend(struct.pack('B', reponse_data['mark1']))  # Mark1
        response.extend(struct.pack('B', reponse_data['mark2']))  # Mark2
        response.extend(struct.pack('B', 0))  # 预留
        
        # 计算并添加检验和
        checksum = MessageParser.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
    
    @staticmethod
    def create_get_program_info_response(program_id: str, version: int, program_manager) -> bytes:
        """创建获取程序信息响应 (8404)"""
        program = program_manager.get_program(program_id, version)
        if not program:
            return None
        
        # 构造响应报文
        response = bytearray()
        response.extend(struct.pack('<H', 0x0048))  # 报文长度 (72字节)
        response.extend(struct.pack('>H', FunctionCode.GET_PROGRAM_INFO_RESP.value))  # 功能代码 8404
        
        # 添加组装好的程序信息（64字节）
        response.extend(program['info'])
        
        # 计算并添加检验和
        checksum = MessageParser.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
    
    @staticmethod
    def create_download_response(program_manager, program_id: str, version: int, 
                                data_index: int, request_length: int) -> bytes:
        """创建下载程序数据响应 (9102)"""
        program = program_manager.get_program(program_id, version)
        if not program:
            return None
        
        program_bytes = program['data']
        
        # 计算实际下发数据
        start_pos = data_index
        end_pos = min(start_pos + request_length, len(program_bytes))
        actual_data = program_bytes[start_pos:end_pos]
        actual_length = len(actual_data)
        
        # 构造响应报文
        response = bytearray()
        response_length = 12 + actual_length
        response.extend(struct.pack('<H', response_length))  # 报文长度
        response.extend(struct.pack('>H', FunctionCode.DOWNLOAD_DATA_RESP.value))  # 功能代码
        response.extend(struct.pack('>I', data_index))  # 程序数据索引
        response.extend(struct.pack('<H', actual_length))  # 下发数据长度
        response.extend(actual_data)  # 程序数据内容
        
        # 计算并添加检验和
        checksum = MessageParser.calculate_checksum(response)
        response.extend(struct.pack('>I', checksum))
        
        return bytes(response)
