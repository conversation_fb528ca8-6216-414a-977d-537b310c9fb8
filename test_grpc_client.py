#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试gRPC客户端
"""

import sys
import logging
from ufn_protocol.grpc_client import GRPCClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_grpc_client():
    """测试gRPC客户端"""
    client = GRPCClient('localhost:50051')
    
    # 测试查询升级信息
    logger.info("Testing query_upgrade_info...")
    result = client.query_upgrade_info('340202420300715', b'NTB2GV31')
    logger.info(f"Result: {result}")
    
    if result:
        # 测试下载文件
        logger.info("Testing download_file_content...")
        file_data = client.download_file_content(result['file_url'])
        if file_data:
            logger.info(f"Downloaded {len(file_data)} bytes")
        else:
            logger.error("Failed to download file")
    
    return result is not None

if __name__ == '__main__':
    success = test_grpc_client()
    sys.exit(0 if success else 1)
